import { LocalStorage, SessionStorage } from './storage';

// 使用示例

// 1. 基本字符串存储
const stringLocalStorage = new LocalStorage<string>();
const stringSessionStorage = new SessionStorage<string>();

// 存储字符串
stringLocalStorage.setItem('username', 'john_doe');
stringSessionStorage.setItem('temp_token', 'abc123');

// 获取字符串
stringLocalStorage.getItem('username').then(username => {
  console.log('Username from localStorage:', username);
});

stringSessionStorage.getItem('temp_token').then(token => {
  console.log('Token from sessionStorage:', token);
});

// 2. 对象存储
interface User {
  id: number;
  name: string;
  email: string;
  preferences: {
    theme: 'light' | 'dark';
    language: string;
  };
}

const userLocalStorage = new LocalStorage<User>();
const userSessionStorage = new SessionStorage<User>();

const user: User = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  preferences: {
    theme: 'dark',
    language: 'zh-CN'
  }
};

// 存储用户对象到 localStorage（持久化）
userLocalStorage.setItem('user_profile', user);

// 存储用户对象到 sessionStorage（会话级别）
userSessionStorage.setItem('current_user', user);

// 获取用户对象
userLocalStorage.getItem('user_profile').then(profile => {
  if (profile) {
    console.log('User profile:', profile);
    console.log('User theme preference:', profile.preferences.theme);
  }
});

userSessionStorage.getItem('current_user').then(currentUser => {
  if (currentUser) {
    console.log('Current user:', currentUser);
  }
});

// 3. 数组存储
interface TodoItem {
  id: number;
  text: string;
  completed: boolean;
  createdAt: Date;
}

const todoLocalStorage = new LocalStorage<TodoItem[]>();
const todoSessionStorage = new SessionStorage<TodoItem[]>();

const todos: TodoItem[] = [
  {
    id: 1,
    text: '完成 SessionStorage 实现',
    completed: true,
    createdAt: new Date()
  },
  {
    id: 2,
    text: '编写测试用例',
    completed: false,
    createdAt: new Date()
  }
];

// 存储待办事项列表
todoLocalStorage.setItem('todos', todos);
todoSessionStorage.setItem('draft_todos', todos);

// 4. 删除和清空操作
async function demonstrateOperations() {
  // 删除特定项
  stringLocalStorage.removeItem('username');
  stringSessionStorage.removeItem('temp_token');

  // 清空所有数据
  // 注意：这会清空整个 localStorage/sessionStorage
  // userLocalStorage.clear();
  // userSessionStorage.clear();

  // 检查项是否存在
  const username = await stringLocalStorage.getItem('username');
  if (username === null) {
    console.log('Username has been removed');
  }

  const token = await stringSessionStorage.getItem('temp_token');
  if (token === null) {
    console.log('Token has been removed');
  }
}

// 5. 错误处理示例
async function handleStorageErrors() {
  try {
    const data = await userLocalStorage.getItem('non_existent_key');
    if (data === null) {
      console.log('Key does not exist');
    }
  } catch (error) {
    console.error('Error accessing localStorage:', error);
  }
}

// 6. 类型安全示例
async function typeSafetyExample() {
  // TypeScript 会确保类型安全
  const userStorage = new LocalStorage<User>();
  
  // 正确的用法
  const validUser: User = {
    id: 1,
    name: 'Jane Doe',
    email: '<EMAIL>',
    preferences: {
      theme: 'light',
      language: 'en-US'
    }
  };
  
  userStorage.setItem('user', validUser);
  
  const retrievedUser = await userStorage.getItem('user');
  if (retrievedUser) {
    // TypeScript 知道 retrievedUser 是 User 类型
    console.log(`User ID: ${retrievedUser.id}`);
    console.log(`User Name: ${retrievedUser.name}`);
    console.log(`Theme: ${retrievedUser.preferences.theme}`);
  }
}

// 7. LocalStorage vs SessionStorage 的区别示例
function storageComparison() {
  console.log('=== Storage Comparison ===');
  
  // LocalStorage: 数据持久化，直到手动删除或清除浏览器数据
  const persistentStorage = new LocalStorage<string>();
  persistentStorage.setItem('persistent_data', 'This will persist across browser sessions');
  
  // SessionStorage: 数据仅在当前会话中有效，关闭标签页后数据消失
  const sessionStorage = new SessionStorage<string>();
  sessionStorage.setItem('session_data', 'This will be cleared when tab is closed');
  
  console.log('LocalStorage: 适用于需要长期保存的数据，如用户偏好设置');
  console.log('SessionStorage: 适用于临时数据，如表单草稿、临时状态等');
}

// 执行示例
demonstrateOperations();
handleStorageErrors();
typeSafetyExample();
storageComparison();

export {
  stringLocalStorage,
  stringSessionStorage,
  userLocalStorage,
  userSessionStorage,
  todoLocalStorage,
  todoSessionStorage
};
