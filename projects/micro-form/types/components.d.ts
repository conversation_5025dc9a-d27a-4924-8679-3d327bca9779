/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ElAside: typeof import('element-plus/es')['ElAside']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElMenuItemGroup: typeof import('element-plus/es')['ElMenuItemGroup']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    Hello_world: typeof import('./../src/components/hello_world.vue')['default']
    HelloWorld: typeof import('./../src/components/HelloWorld.vue')['default']
    IconCommunity: typeof import('./../src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./../src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./../src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./../src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./../src/components/icons/IconTooling.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TheWelcome: typeof import('./../src/components/TheWelcome.vue')['default']
    View_component: typeof import('./../src/components/view_component.vue')['default']
    WelcomeItem: typeof import('./../src/components/WelcomeItem.vue')['default']
  }
}
